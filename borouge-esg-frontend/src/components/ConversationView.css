.conversation-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, var(--background-white) 0%, #fafbff 100%);
  max-width: 100%;
  margin: 0 auto;
}

/* Header */
.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 48px;
  background: var(--background-white);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: transparent;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: var(--background-gray);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.conversation-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--primary-blue);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #0052a3;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Messages */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px 48px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 90%;
}

.message.user {
  align-self: flex-end;
}

.message.assistant {
  align-self: flex-start;
}

.user-message {
  background: linear-gradient(135deg, var(--primary-blue) 0%, #0052a3 100%);
  color: white;
  padding: 16px 20px;
  border-radius: var(--radius-xl);
  border-bottom-right-radius: var(--radius-sm);
  box-shadow: var(--shadow-md);
}

.message-content {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
}

.ai-message {
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  border-bottom-left-radius: var(--radius-sm);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.ai-response {
  padding: 24px;
}

/* Intelligence Report */
.intelligence-report {
  max-width: none;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--border-light);
}

.report-title-section h3 {
  font-size: 22px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.report-meta {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: var(--text-secondary);
}

.report-timestamp {
  font-weight: 500;
}

.report-confidence {
  background: #dcfce7;
  color: #16a34a;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 600;
}

/* Executive Summary */
.executive-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-left: 4px solid var(--primary-blue);
  padding: 20px;
  margin-bottom: 24px;
  border-radius: var(--radius-lg);
}

.executive-summary h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.executive-summary p {
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-secondary);
  margin: 0;
}

/* Market Impact Section */
.market-impact-section {
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 20px;
  margin-bottom: 24px;
}

.market-impact-section h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.impact-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.impact-metric {
  display: flex;
  flex-direction: column;
  padding: 16px;
  background: var(--background-gray);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-blue);
}

.metric-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 16px;
  font-weight: 700;
  color: var(--text-primary);
}

.metric-value.risk {
  color: #dc2626;
}

.metric-value.investment {
  color: #d97706;
}

.metric-value.timeline {
  color: var(--primary-blue);
}

.metric-value.opportunity {
  color: var(--primary-green);
}

.copy-btn {
  padding: 8px;
  background: transparent;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background: var(--background-gray);
  color: var(--text-primary);
}

.report-summary {
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: 24px;
}

/* Key Findings */
.key-findings h4,
.sources-section h4,
.action-items h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.finding-card {
  background: var(--background-gray);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 20px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.finding-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.finding-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.finding-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.finding-card .finding-icon {
  background: var(--primary-blue);
}

.finding-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.impact-badge {
  padding: 4px 12px;
  border-radius: var(--radius-xl);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.impact-badge.high {
  background: #fee2e2;
  color: #dc2626;
}

.impact-badge.medium {
  background: #fef3c7;
  color: #d97706;
}

.impact-badge.low {
  background: #dcfce7;
  color: #16a34a;
}

.finding-description {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.confidence-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.confidence-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 100px;
}

.confidence-progress {
  flex: 1;
  height: 6px;
  background: var(--border-light);
  border-radius: 3px;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green) 0%, var(--primary-blue) 100%);
  border-radius: 3px;
}

/* Sources */
.sources-section {
  margin-top: 32px;
}

.sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.source-card {
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 16px;
  transition: all 0.2s ease;
}

.source-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.source-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.source-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--primary-blue);
}

.source-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-tertiary);
}

/* Action Items */
.action-items {
  margin-top: 32px;
}

.action-items ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.action-items li {
  padding: 12px 16px;
  background: var(--background-gray);
  border-left: 4px solid var(--primary-blue);
  border-radius: var(--radius-md);
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-secondary);
}

/* Loading */
.loading-message {
  align-self: flex-start;
  max-width: 85%;
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  border-bottom-left-radius: var(--radius-sm);
  color: var(--text-secondary);
  font-size: 16px;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
  align-self: flex-start;
  max-width: 85%;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--radius-xl);
  border-bottom-left-radius: var(--radius-sm);
  color: #dc2626;
  font-size: 16px;
}

.error-icon {
  flex-shrink: 0;
}

.error-dismiss {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  margin-left: auto;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.error-dismiss:hover {
  background-color: #fee2e2;
}

/* Input */
.message-input-container {
  padding: 24px 48px;
  background: var(--background-white);
  border-top: 1px solid var(--border-light);
}

.message-input-box {
  display: flex;
  align-items: flex-end;
  gap: 16px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.message-input {
  flex: 1;
  min-height: 48px;
  max-height: 120px;
  padding: 14px 20px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  font-size: 16px;
  font-family: inherit;
  resize: none;
  outline: none;
  transition: all 0.2s ease;
}

.message-input:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.send-btn {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-blue) 0%, #0052a3 100%);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .conversation-header {
    padding: 16px 20px;
    max-width: 100%;
  }

  .messages-container {
    padding: 16px 20px;
    max-width: 100%;
  }

  .message {
    max-width: 95%;
  }

  .message-input-container {
    padding: 16px 20px;
  }

  .message-input-box {
    max-width: 100%;
  }

  .sources-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .conversation-header {
    padding: 12px 16px;
  }

  .messages-container {
    padding: 12px 16px;
  }

  .message-input-container {
    padding: 12px 16px;
  }
}

/* Enhanced Finding Cards */
.finding-header {
  align-items: flex-start !important;
  margin-bottom: 16px !important;
}

.finding-icon {
  width: 36px !important;
  height: 36px !important;
  border-radius: 50% !important;
  flex-shrink: 0;
}

.finding-badges {
  display: flex;
  gap: 8px;
  flex-direction: column;
  align-items: flex-end;
}

.urgency-badge {
  padding: 4px 10px;
  border-radius: var(--radius-sm);
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.urgency-badge.critical {
  background: #7f1d1d;
  color: white;
}

.urgency-badge.high {
  background: #dc2626;
  color: white;
}

.urgency-badge.medium {
  background: #d97706;
  color: white;
}

.urgency-badge.low {
  background: #16a34a;
  color: white;
}

.finding-details {
  background: var(--background-gray);
  padding: 16px;
  border-radius: var(--radius-md);
  margin: 12px 0;
  border-left: 3px solid var(--primary-blue);
}

.finding-details p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-secondary);
}

.finding-timeline {
  background: #eff6ff;
  padding: 8px 12px;
  border-radius: var(--radius-sm);
  margin-top: 12px;
  font-size: 13px;
  color: var(--primary-blue);
  border: 1px solid #dbeafe;
}

/* Strategic Recommendations */
.strategic-recommendations {
  margin-top: 32px;
}

.strategic-recommendations h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
}

.recommendation-card {
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 20px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.recommendation-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.priority-badge {
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-badge.critical {
  background: #7f1d1d;
  color: white;
}

.priority-badge.high {
  background: #dc2626;
  color: white;
}

.priority-badge.medium {
  background: #d97706;
  color: white;
}

.recommendation-investment {
  font-size: 16px;
  font-weight: 700;
  color: var(--primary-blue);
}

.recommendation-action {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 8px 0;
}

.recommendation-description {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-secondary);
  margin-bottom: 12px;
}

.recommendation-timeline {
  background: #eff6ff;
  padding: 8px 12px;
  border-radius: var(--radius-sm);
  font-size: 13px;
  color: var(--primary-blue);
  border: 1px solid #dbeafe;
}

/* Competitive Benchmarking */
.competitive-benchmarking {
  margin-top: 32px;
}

.competitive-benchmarking h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
}

.competitors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.competitor-card {
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 20px;
  transition: all 0.2s ease;
}

.competitor-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.competitor-name {
  font-size: 16px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.competitor-strategy {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.competitor-analysis {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.advantage, .weakness {
  font-size: 13px;
  line-height: 1.4;
  padding: 8px 12px;
  border-radius: var(--radius-sm);
}

.advantage {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.weakness {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* Risk Assessment */
.risk-assessment {
  margin-top: 32px;
}

.risk-assessment h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
}

.risk-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.risk-category {
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 20px;
}

.risk-category h6 {
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
}

.risk-category ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.risk-category li {
  padding: 8px 0;
  font-size: 13px;
  line-height: 1.4;
  border-bottom: 1px solid var(--border-light);
}

.risk-category li:last-child {
  border-bottom: none;
}

.high-risk {
  border-left: 4px solid #dc2626;
}

.high-risk h6 {
  color: #dc2626;
}

.medium-risk {
  border-left: 4px solid #d97706;
}

.medium-risk h6 {
  color: #d97706;
}

.low-risk {
  border-left: 4px solid #16a34a;
}

.low-risk h6 {
  color: #16a34a;
}

/* Simplified Report Styles */
.intelligence-report.simplified {
  max-width: none;
  padding: 24px;
  background: var(--background-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.report-actions {
  display: flex;
  gap: 8px;
}

.copy-btn.secondary {
  background: var(--background-gray);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
  padding: 8px;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.copy-btn.secondary:hover {
  background: var(--border-light);
  color: var(--text-primary);
}

/* Problem-Solution Summary */
.problem-solution-summary {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #fef2f2 0%, #fef7f7 100%);
  border-radius: var(--radius-lg);
  border-left: 4px solid #dc2626;
}

.problem-statement, .opportunity-statement {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.problem-icon, .opportunity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.problem-icon {
  background: #dc2626;
  color: white;
}

.opportunity-icon {
  background: var(--primary-green);
  color: white;
}

.problem-content h4, .opportunity-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.problem-content p, .opportunity-content p {
  font-size: 15px;
  line-height: 1.5;
  color: var(--text-secondary);
  margin-bottom: 12px;
}

.impact-highlight {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.impact-text, .urgency-text {
  font-size: 13px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
}

.impact-text {
  background: #fef3c7;
  color: #d97706;
}

.urgency-text {
  background: #fef2f2;
  color: #dc2626;
}

/* Key Insights */
.key-insights {
  margin-bottom: 32px;
}

.key-insights h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.insight-card {
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 20px;
  transition: all 0.2s ease;
}

.insight-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.impact-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.impact-indicator.critical {
  background: #dc2626;
}

.impact-indicator.high {
  background: #d97706;
}

.impact-indicator.medium {
  background: var(--primary-blue);
}

.impact-label {
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.impact-label.critical {
  color: #dc2626;
}

.impact-label.high {
  color: #d97706;
}

.impact-label.medium {
  color: var(--primary-blue);
}

.insight-card h5 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  line-height: 1.3;
}

.insight-card p {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-secondary);
  margin-bottom: 12px;
}

.quick-action {
  background: #eff6ff;
  padding: 8px 12px;
  border-radius: var(--radius-sm);
  font-size: 13px;
  color: var(--primary-blue);
  border: 1px solid #dbeafe;
}

/* Next Steps */
.next-steps {
  margin-bottom: 32px;
}

.next-steps h4 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step-card {
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 20px;
  transition: all 0.2s ease;
}

.step-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.step-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.priority-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: white;
  font-size: 14px;
}

.priority-indicator.critical {
  background: #dc2626;
}

.priority-indicator.high {
  background: #d97706;
}

.priority-indicator.medium {
  background: var(--primary-blue);
}

.step-meta {
  display: flex;
  gap: 16px;
  align-items: center;
}

.step-timeline, .step-investment {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
}

.step-card h5 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.step-card p {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-secondary);
}

/* Enhanced Collapsible Sections */
.detailed-sections {
  margin-top: 40px;
  border-top: 2px solid var(--border-light);
  padding-top: 32px;
}

.collapsible-section {
  margin-bottom: 24px;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.collapsible-section:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.section-toggle {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  font-size: 16px;
  font-weight: 700;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.section-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-blue) 0%, #3b82f6 100%);
  transition: width 0.3s ease;
}

.section-toggle:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: var(--primary-blue);
  transform: translateY(-1px);
}

.section-toggle:hover::before {
  width: 8px;
}

.section-content {
  padding: 32px;
  border: 2px solid var(--border-light);
  border-top: none;
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  background: linear-gradient(135deg, var(--background-white) 0%, #fafbfc 100%);
  position: relative;
}

.section-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--border-light) 50%, transparent 100%);
}

/* Enhanced Detailed Findings */
.detailed-findings {
  display: grid;
  gap: 24px;
}

.detailed-findings .finding-card {
  background: var(--background-white);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: 28px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.detailed-findings .finding-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  transition: all 0.3s ease;
}

.detailed-findings .finding-card[data-type="regulatory"]::before {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}

.detailed-findings .finding-card[data-type="financial"]::before {
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
}

.detailed-findings .finding-card[data-type="competitive"]::before {
  background: linear-gradient(135deg, var(--primary-blue) 0%, #3b82f6 100%);
}

.detailed-findings .finding-card[data-type="market"]::before {
  background: linear-gradient(135deg, var(--primary-green) 0%, #22c55e 100%);
}

.detailed-findings .finding-card[data-type="technology"]::before {
  background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
}

.detailed-findings .finding-card[data-type="environmental"]::before {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

.detailed-findings .finding-card[data-type="social"]::before {
  background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
}

.detailed-findings .finding-card[data-type="governance"]::before {
  background: linear-gradient(135deg, #4338ca 0%, #6366f1 100%);
}

.detailed-findings .finding-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: var(--primary-blue);
}

.detailed-findings .finding-card:hover::before {
  width: 12px;
}

/* Enhanced Finding Headers */
.detailed-findings .finding-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-light);
  position: relative;
}

.detailed-findings .finding-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.detailed-findings .finding-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  border-radius: 50%;
}

.detailed-findings .finding-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.3;
  flex: 1;
  margin-bottom: 8px;
}

.detailed-findings .finding-badges {
  display: flex;
  gap: 12px;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
}

.detailed-findings .impact-badge,
.detailed-findings .urgency-badge {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.detailed-findings .impact-badge::before,
.detailed-findings .urgency-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
  border-radius: 20px;
}

.detailed-findings .impact-badge.high {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  color: #dc2626;
  border: 2px solid #fecaca;
}

.detailed-findings .impact-badge.medium {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  color: #d97706;
  border: 2px solid #fed7aa;
}

.detailed-findings .impact-badge.low {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  color: #16a34a;
  border: 2px solid #bbf7d0;
}

.detailed-findings .urgency-badge.critical {
  background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
  color: white;
  border: 2px solid #7f1d1d;
}

.detailed-findings .urgency-badge.high {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  color: white;
  border: 2px solid #dc2626;
}

.detailed-findings .urgency-badge.medium {
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
  color: white;
  border: 2px solid #d97706;
}

/* Enhanced Finding Content */
.detailed-findings .finding-description {
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-secondary);
  margin-bottom: 20px;
  font-weight: 500;
}

.detailed-findings .finding-details {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 24px;
  border-radius: var(--radius-lg);
  margin: 20px 0;
  border-left: 4px solid var(--primary-blue);
  position: relative;
  overflow: hidden;
}

.detailed-findings .finding-details::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, transparent 100%);
  border-radius: 50%;
  transform: translate(20px, -20px);
}

.detailed-findings .finding-details p {
  margin: 0;
  font-size: 15px;
  line-height: 1.6;
  color: var(--text-secondary);
  font-weight: 500;
}

.detailed-findings .finding-timeline {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  padding: 16px 20px;
  border-radius: var(--radius-lg);
  margin-top: 20px;
  font-size: 14px;
  color: var(--primary-blue);
  border: 2px solid #dbeafe;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.detailed-findings .finding-timeline::before {
  content: '⏱️';
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  font-size: 18px;
  opacity: 0.7;
}

/* Enhanced Confidence Bar */
.detailed-findings .confidence-bar {
  margin-top: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.detailed-findings .confidence-label {
  font-size: 14px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detailed-findings .confidence-progress {
  height: 8px;
  background: var(--border-light);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.detailed-findings .confidence-fill {
  height: 100%;
  background: linear-gradient(135deg, var(--primary-blue) 0%, #3b82f6 100%);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.detailed-findings .confidence-fill::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Enhanced Sources Section */
.sources-section {
  margin-top: 8px;
}

.sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.source-card {
  background: var(--background-white);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.source-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-blue) 0%, #3b82f6 100%);
  transition: width 0.3s ease;
}

.source-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: var(--primary-blue);
}

.source-card:hover::before {
  width: 8px;
}

.source-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.source-header svg {
  color: var(--primary-blue);
  flex-shrink: 0;
  margin-top: 2px;
}

.source-title {
  font-size: 16px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.4;
  flex: 1;
  min-width: 0;
}

.source-confidence {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  white-space: nowrap;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.source-confidence::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
  border-radius: 16px;
}

.source-confidence.official {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #16a34a;
  border: 2px solid #bbf7d0;
}

.source-confidence.high {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: var(--primary-blue);
  border: 2px solid #bfdbfe;
}

.source-confidence.medium {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  color: #d97706;
  border: 2px solid #fed7aa;
}

.source-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.source-url {
  font-size: 13px;
  color: var(--primary-blue);
  font-weight: 600;
  text-decoration: none;
  word-break: break-all;
  padding: 8px 12px;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-radius: var(--radius-md);
  border: 1px solid #dbeafe;
  transition: all 0.2s ease;
}

.source-url:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  transform: translateX(2px);
}

.source-date {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 600;
  padding: 4px 8px;
  background: var(--background-gray);
  border-radius: var(--radius-sm);
  display: inline-block;
  width: fit-content;
}

.source-type {
  background: linear-gradient(135deg, var(--background-gray) 0%, #f1f5f9 100%);
  padding: 6px 12px;
  border-radius: var(--radius-md);
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
  display: inline-block;
  width: fit-content;
  letter-spacing: 0.5px;
}

/* Borouge-Specific Visual Elements */
.borouge-recommendation {
  background: linear-gradient(135deg, #0066cc 0%, #004499 100%);
  color: white;
  border: 3px solid #0066cc;
  position: relative;
  overflow: hidden;
}

.borouge-recommendation::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.borouge-recommendation::after {
  content: 'BOROUGE STRATEGIC';
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 9px;
  font-weight: 800;
  letter-spacing: 0.5px;
  backdrop-filter: blur(10px);
}

.borouge-recommendation .finding-title,
.borouge-recommendation .finding-description {
  color: white;
}

.borouge-recommendation .finding-details {
  background: rgba(255, 255, 255, 0.15);
  border-left-color: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
}

.borouge-recommendation .finding-details p {
  color: rgba(255, 255, 255, 0.9);
}

.borouge-recommendation .finding-timeline {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
}

.borouge-recommendation .confidence-bar {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.borouge-recommendation .confidence-label {
  color: white;
}

.borouge-recommendation .confidence-fill {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
}

/* Investment & Financial Highlights */
.financial-highlight {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  border: 2px solid #f59e0b;
  border-radius: var(--radius-xl);
  padding: 20px;
  margin: 16px 0;
  position: relative;
  overflow: hidden;
}

.financial-highlight::before {
  content: '💰';
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 24px;
  opacity: 0.7;
}

.financial-highlight .amount {
  font-size: 24px;
  font-weight: 800;
  color: #d97706;
  margin-bottom: 8px;
}

.financial-highlight .description {
  font-size: 14px;
  color: #92400e;
  font-weight: 600;
}

/* Competitive Intelligence Styling */
.competitive-insight {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 2px solid var(--primary-blue);
  border-radius: var(--radius-xl);
  padding: 20px;
  margin: 16px 0;
  position: relative;
  overflow: hidden;
}

.competitive-insight::before {
  content: '🏆';
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 24px;
  opacity: 0.7;
}

.competitive-insight .competitor-name {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-blue);
  margin-bottom: 8px;
}

.competitive-insight .advantage {
  color: #16a34a;
  font-weight: 600;
}

.competitive-insight .threat {
  color: #dc2626;
  font-weight: 600;
}

/* Risk Assessment Visual Elements */
.risk-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 4px 0;
}

.risk-indicator.critical {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  color: #dc2626;
  border: 2px solid #fecaca;
}

.risk-indicator.high {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  color: #d97706;
  border: 2px solid #fed7aa;
}

.risk-indicator.medium {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  color: var(--primary-blue);
  border: 2px solid #bfdbfe;
}

.risk-indicator.low {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  color: #16a34a;
  border: 2px solid #bbf7d0;
}

/* Multi-Article Response System */
.multi-article-response {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.response-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 24px;
  border-radius: var(--radius-xl);
  border: 2px solid var(--border-light);
  margin-bottom: 32px;
}

.response-header h3 {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.articles-summary {
  display: flex;
  gap: 24px;
  align-items: center;
  flex-wrap: wrap;
}

.articles-count {
  background: linear-gradient(135deg, var(--primary-blue) 0%, #3b82f6 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(0, 102, 204, 0.3);
}

.priority-note {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 600;
  font-style: italic;
}

/* Article Container */
.article-container {
  position: relative;
  margin-bottom: 48px;
}

.article-container:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: -24px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, var(--border-light) 20%, var(--border-medium) 50%, var(--border-light) 80%, transparent 100%);
}

.article-priority-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
}

.priority-badge-large {
  background: linear-gradient(135deg, var(--background-white) 0%, #f8fafc 100%);
  border: 3px solid var(--primary-blue);
  border-radius: 24px;
  padding: 16px 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  box-shadow: 0 8px 24px rgba(0, 102, 204, 0.15);
  position: relative;
  overflow: hidden;
}

.priority-badge-large::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 102, 204, 0.05) 0%, transparent 100%);
  pointer-events: none;
}

.priority-number {
  font-size: 28px;
  font-weight: 800;
  color: var(--primary-blue);
  line-height: 1;
}

.priority-label {
  font-size: 14px;
  font-weight: 700;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
  text-align: center;
}

/* Article-specific styling */
.article-container .intelligence-report {
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.article-container .intelligence-report::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-blue) 0%, #3b82f6 100%);
}

.article-container:hover .intelligence-report {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  border-color: var(--primary-blue);
}

.article-container:hover .intelligence-report::before {
  width: 12px;
}

/* Priority-based styling */
.article-container:nth-child(1) .priority-badge-large {
  border-color: #dc2626;
}

.article-container:nth-child(1) .priority-number {
  color: #dc2626;
}

.article-container:nth-child(1) .intelligence-report::before {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}

.article-container:nth-child(2) .priority-badge-large {
  border-color: #d97706;
}

.article-container:nth-child(2) .priority-number {
  color: #d97706;
}

.article-container:nth-child(2) .intelligence-report::before {
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
}

.article-container:nth-child(3) .priority-badge-large {
  border-color: var(--primary-blue);
}

.article-container:nth-child(4) .priority-badge-large {
  border-color: var(--primary-green);
}

.article-container:nth-child(4) .priority-number {
  color: var(--primary-green);
}

.article-container:nth-child(4) .intelligence-report::before {
  background: linear-gradient(135deg, var(--primary-green) 0%, #22c55e 100%);
}

.article-container:nth-child(n+5) .priority-badge-large {
  border-color: #6b7280;
}

.article-container:nth-child(n+5) .priority-number {
  color: #6b7280;
}

.article-container:nth-child(n+5) .intelligence-report::before {
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
}

/* Executive Summary Styling */
.executive-summary {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  padding: 24px;
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--primary-blue);
  margin-bottom: 24px;
}

.executive-summary h4 {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-blue);
  margin-bottom: 12px;
}

.executive-summary p {
  font-size: 15px;
  line-height: 1.6;
  color: var(--text-secondary);
  margin: 0;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .intelligence-report.simplified {
    padding: 16px;
  }

  .problem-solution-summary {
    padding: 16px;
    gap: 16px;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .step-meta {
    gap: 12px;
  }

  .detailed-findings .finding-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .detailed-findings .finding-badges {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .sources-grid {
    grid-template-columns: 1fr;
  }

  .section-content {
    padding: 20px;
  }

  .collapsible-section {
    margin-bottom: 16px;
  }

  .section-toggle {
    padding: 16px 20px;
    font-size: 14px;
  }

  /* Multi-article mobile adjustments */
  .multi-article-response {
    gap: 32px;
  }

  .response-header {
    padding: 20px;
    margin-bottom: 24px;
  }

  .response-header h3 {
    font-size: 20px;
  }

  .articles-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .priority-badge-large {
    padding: 12px 24px;
  }

  .priority-number {
    font-size: 24px;
  }

  .priority-label {
    font-size: 12px;
  }

  .article-container {
    margin-bottom: 32px;
  }

  .article-container:not(:last-child)::after {
    bottom: -16px;
    width: 90%;
  }
}

/* Article List View Styles */
.articles-list-view {
  margin: 20px 0;
}

.articles-header {
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid var(--border-light);
}

.articles-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.articles-summary {
  display: flex;
  gap: 16px;
  align-items: center;
  font-size: 14px;
}

.articles-count {
  color: var(--primary-purple);
  font-weight: 600;
}

.priority-note {
  color: var(--text-secondary);
}

.articles-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 20px;
}

.article-preview-card {
  background: #ffffff;
  border: 2px solid var(--border-light);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: visible;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  width: 100%;
  min-height: 160px;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.article-preview-card:hover {
  border-color: var(--primary-purple);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* Priority-based color coding with maximum text contrast */
.article-preview-card.priority-critical {
  border-color: #dc2626;
  background: #ffffff;
  border-left: 5px solid #dc2626;
}

.article-preview-card.priority-critical:hover {
  border-color: #b91c1c;
  box-shadow: 0 8px 24px rgba(220, 38, 38, 0.12);
  background: #ffffff;
}

.article-preview-card.priority-high {
  border-color: #ea580c;
  background: #ffffff;
  border-left: 5px solid #ea580c;
}

.article-preview-card.priority-high:hover {
  border-color: #c2410c;
  box-shadow: 0 8px 24px rgba(234, 88, 12, 0.12);
  background: #ffffff;
}

.article-preview-card.priority-medium {
  border-color: #ca8a04;
  background: #ffffff;
  border-left: 5px solid #ca8a04;
}

.article-preview-card.priority-medium:hover {
  border-color: #a16207;
  box-shadow: 0 8px 24px rgba(202, 138, 4, 0.12);
  background: #ffffff;
}

.article-preview-card.priority-opportunity {
  border-color: #16a34a;
  background: #ffffff;
  border-left: 5px solid #16a34a;
}

.article-preview-card.priority-opportunity:hover {
  border-color: #15803d;
  box-shadow: 0 8px 24px rgba(22, 163, 74, 0.12);
  background: #ffffff;
}

.article-preview-card.priority-strategic {
  border-color: #2563eb;
  background: #ffffff;
  border-left: 5px solid #2563eb;
}

.article-preview-card.priority-strategic:hover {
  border-color: #1d4ed8;
  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.12);
  background: #ffffff;
}

.article-priority-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, var(--primary-purple) 0%, #6366f1 100%);
  color: white;
  border-radius: 24px;
  font-size: 13px;
  font-weight: 600;
  flex-shrink: 0;
  min-width: 140px;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.priority-number {
  background: rgba(255, 255, 255, 0.25);
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 700;
}

/* Priority badge color variations */
.priority-critical .article-priority-badge {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.priority-high .article-priority-badge {
  background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
}

.priority-medium .article-priority-badge {
  background: linear-gradient(135deg, #ca8a04 0%, #a16207 100%);
}

.priority-opportunity .article-priority-badge {
  background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
}

.priority-strategic .article-priority-badge {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

.article-preview-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 0; /* Allows content to shrink */
  padding: 4px 12px 4px 0;
  z-index: 2;
  position: relative;
}

.article-title {
  font-size: 18px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  /* Allow title to wrap to multiple lines */
  white-space: normal;
  max-width: 100%;
}

.article-summary {
  margin-bottom: 0;
}

.problem-preview,
.executive-summary-preview {
  color: #374151;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  font-weight: 500;
}

.article-impact-indicators {
  display: flex;
  gap: 8px;
  margin-bottom: 0;
  flex-wrap: wrap;
}

.impact-indicator,
.urgency-indicator,
.revenue-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  white-space: nowrap;
  border: 1px solid;
  transition: all 0.2s ease;
}

.impact-indicator {
  background: #fef2f2;
  color: #991b1b;
  border-color: #fecaca;
}

.impact-indicator:hover {
  background: #fee2e2;
  transform: translateY(-1px);
}

.urgency-indicator {
  background: #fff7ed;
  color: #c2410c;
  border-color: #fed7aa;
}

.urgency-indicator:hover {
  background: #ffedd5;
  transform: translateY(-1px);
}

.revenue-indicator {
  background: #f0fdf4;
  color: #166534;
  border-color: #bbf7d0;
}

.revenue-indicator:hover {
  background: #dcfce7;
  transform: translateY(-1px);
}

.article-preview-footer {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  flex-shrink: 0;
  min-width: 140px;
  gap: 10px;
  padding: 4px 0;
  z-index: 2;
  position: relative;
}

.read-more {
  color: #4f46e5;
  font-size: 13px;
  font-weight: 600;
  white-space: nowrap;
  padding: 6px 12px;
  border-radius: 6px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.read-more:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateX(-2px);
  color: #3730a3;
}

/* Article Detail View Styles */
.article-detail-view {
  margin: 20px 0;
}

.article-detail-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid var(--border-light);
}

.article-detail-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* Contextual Response Styles */
.contextual-response {
  margin: 20px 0;
  padding: 20px;
  background: var(--background-white);
  border: 1px solid var(--border-light);
  border-radius: 12px;
}

.contextual-response .response-header {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light);
}

.contextual-response .response-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.context-note {
  font-size: 14px;
  color: var(--text-secondary);
}

.contextual-content {
  font-size: 15px;
  line-height: 1.6;
  color: var(--text-primary);
}

.related-findings {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-light);
}

.related-findings h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
}

.finding-summary {
  margin-bottom: 12px;
  padding: 12px;
  background: rgba(99, 102, 241, 0.05);
  border-radius: 8px;
}

.finding-summary strong {
  color: var(--text-primary);
  font-weight: 600;
}

.finding-summary p {
  margin: 4px 0 0 0;
  color: var(--text-secondary);
  font-size: 14px;
}

/* Additional responsive styles for new components */
@media (max-width: 768px) {
  .articles-grid {
    gap: 14px;
  }

  .article-preview-card {
    padding: 18px;
    flex-direction: column;
    align-items: flex-start;
    gap: 14px;
    min-height: 180px;
  }

  .article-priority-badge {
    min-width: auto;
    align-self: flex-start;
    padding: 8px 14px;
    min-width: 120px;
  }

  .article-preview-content {
    width: 100%;
    gap: 10px;
    padding: 0;
  }

  .article-title {
    font-size: 17px;
    line-height: 1.3;
    font-weight: 700;
    color: #111827;
  }

  .problem-preview,
  .executive-summary-preview {
    font-size: 13px;
    -webkit-line-clamp: 2;
    color: #374151;
  }

  .article-preview-footer {
    align-items: flex-start;
    flex-direction: row;
    justify-content: space-between;
    min-width: auto;
    width: 100%;
    gap: 12px;
    padding: 0;
  }

  .article-impact-indicators {
    gap: 6px;
    flex-wrap: wrap;
  }

  .article-detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .articles-header {
    padding: 16px;
  }

  .articles-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .citations-grid {
    grid-template-columns: 1fr;
  }
}

/* Real Citations Section */
.citations-section {
  margin-top: 16px;
}

.citations-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.citation-card {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid #e2e8f0;
  border-radius: var(--radius-lg);
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.citation-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), #10b981);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.citation-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.citation-card:hover::before {
  opacity: 1;
}

.citation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.citation-source-type {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.source-type-badge {
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.source-type-badge.government {
  background: #dcfce7;
  color: #166534;
}

.source-type-badge.international-organization {
  background: #dbeafe;
  color: #1e40af;
}

.source-type-badge.consulting {
  background: #f3e8ff;
  color: #7c3aed;
}

.source-type-badge.financial-news {
  background: #fef3c7;
  color: #92400e;
}

.source-type-badge.academic {
  background: #e0f2fe;
  color: #0277bd;
}

.source-type-badge.industry {
  background: #fce7f3;
  color: #be185d;
}

.credibility-score {
  font-size: 10px;
  color: #10b981;
  font-weight: 600;
}

.citation-content {
  flex: 1;
}

.citation-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
}

.citation-link {
  color: var(--text-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.citation-link:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.citation-snippet {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 8px 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.citation-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 12px;
  color: var(--text-secondary);
  border-top: 1px solid #f1f5f9;
  padding-top: 8px;
}

.citation-domain {
  color: var(--primary-color);
  font-weight: 500;
}

.citation-date {
  color: #64748b;
}

.relevance-score {
  color: #10b981;
  font-weight: 500;
}

.no-citations {
  text-align: center;
  padding: 32px;
  color: var(--text-secondary);
  font-style: italic;
}
