// Simplified AI Service for Borouge ESG Intelligence Platform
// Single Google Gemini provider with conversational follow-up support

const crypto = require('crypto');
const ResponseParser = require('./responseParser');
const SearchService = require('./searchService');

class AIService {
  constructor(config, supabase) {
    this.config = config;
    this.supabase = supabase;
    this.responseParser = new ResponseParser();
    this.searchService = new SearchService(config);

    // Conversation history storage for follow-up context
    this.conversationHistory = new Map();
    this.conversationTimeout = 3600000; // 1 hour conversation context timeout

    // Simple rate limiting for Gemini
    this.rateLimitTracking = [];
    this.rateLimitWindow = 60000; // 1 minute window
    this.rateLimitMax = 60; // Conservative limit for Gemini free tier

    console.log('🚀 Simplified AI Service initialized with Gemini-only provider');
  }

  // Generate query hash for caching
  generateQueryHash(query) {
    return crypto.createHash('sha256').update(query.toLowerCase().trim()).digest('base64');
  }

  // Simple rate limiting for Gemini API
  checkRateLimit() {
    const now = Date.now();
    const windowStart = now - this.rateLimitWindow;

    // Clean old requests
    this.rateLimitTracking = this.rateLimitTracking.filter(time => time > windowStart);

    if (this.rateLimitTracking.length >= this.rateLimitMax) {
      return false;
    }

    this.rateLimitTracking.push(now);
    return true;
  }

  // Generate conversation ID for follow-up context
  generateConversationId(query, userId = 'default') {
    return crypto.createHash('md5').update(`${userId}_${Date.now()}`).digest('hex');
  }

  // Store conversation context for follow-ups
  storeConversationContext(conversationId, originalQuery, response) {
    this.conversationHistory.set(conversationId, {
      originalQuery,
      response,
      timestamp: Date.now(),
      followUpCount: 0
    });

    // Clean old conversations
    this.cleanOldConversations();
  }

  // Get conversation context for follow-up
  getConversationContext(conversationId) {
    const context = this.conversationHistory.get(conversationId);
    if (context && Date.now() - context.timestamp < this.conversationTimeout) {
      return context;
    }
    return null;
  }

  // Clean old conversation contexts
  cleanOldConversations() {
    const now = Date.now();
    for (const [id, context] of this.conversationHistory.entries()) {
      if (now - context.timestamp > this.conversationTimeout) {
        this.conversationHistory.delete(id);
      }
    }
  }

  // Borouge-specific ESG context for AI prompts
  getBorogueContext() {
    return `You are an ESG intelligence analyst for Borouge, a UAE-based petrochemical company (ADNOC subsidiary).

Company Context:
- Products: 5M tonnes polyolefins (polypropylene/polyethylene annually)
- EU Exports: €2.3B annually (major revenue stream)
- Asian Markets: $1.8B annually
- Key Competitors: SABIC, Dow Chemical, ExxonMobil Chemical, LyondellBasell
- Parent Company: ADNOC (renewable energy transition underway)
- Location: UAE (carbon neutrality 2050 commitment)
- Business Focus: Sustainable petrochemicals, circular economy, ESG compliance

Your task is to analyze ESG queries and provide structured business intelligence specifically relevant to Borouge's operations, market position, and strategic challenges.`;
  }

  // Create structured prompt for AI analysis with conversational support
  createAnalysisPrompt(query, isFollowUp = false, conversationContext = null) {
    const baseContext = this.getBorogueContext();

    let contextualPrompt = baseContext;

    if (isFollowUp && conversationContext) {
      contextualPrompt += `

CONVERSATION CONTEXT:
Original Query: "${conversationContext.originalQuery}"
Previous Response Summary: ${conversationContext.response.executiveSummary}
Previous Articles: ${conversationContext.response.articles.map(a => `${a.priorityLabel}: ${a.executiveSummary}`).join('; ')}
Follow-up Count: ${conversationContext.followUpCount + 1}

This is a FOLLOW-UP question related to the above context. Provide a focused response that:
1. References the previous analysis when relevant
2. Maintains Borouge ESG context
3. Provides specific, actionable insights
4. Builds upon the previous conversation`;
    }

    let prompt = `${contextualPrompt}

Current Query: "${query}"

CRITICAL: Return ONLY a valid JSON object. Do not include any text before or after the JSON. Do not use markdown formatting or code blocks. Start directly with { and end with }.

${isFollowUp ? 'For this FOLLOW-UP query, provide a focused response in this JSON structure:' : 'Provide analysis in this exact JSON structure:'}
{
  "executiveSummary": "2-3 sentence summary of business impact for Borouge executives",
  "articles": [
    {
      "articleId": 1,
      "reportType": "Critical Regulatory Compliance Analysis",
      "priorityLabel": "CRITICAL REGULATORY COMPLIANCE",
      "priority": "HIGH",
      "executiveSummary": "Executive summary specific to this article",
      "keyFindings": [
        {
          "priority": "HIGH",
          "title": "Finding title",
          "description": "Detailed description with specific numbers/impact",
          "businessImpact": "Direct impact on Borouge operations/financials"
        }
      ],
      "detailedAnalysis": "Comprehensive analysis with regulatory details, competitive implications, and strategic recommendations",
      "financialImpact": {
        "shortTerm": "0-2 years impact with specific numbers",
        "longTerm": "3-5 years impact with specific numbers",
        "investmentRequired": "Estimated investment needed"
      },
      "actionItems": [
        "Specific actionable next steps for Borouge teams"
      ],
      "citations": []
    },
    {
      "articleId": 2,
      "reportType": "High Financial Impact Assessment",
      "priorityLabel": "HIGH FINANCIAL IMPACT",
      "priority": "HIGH",
      "executiveSummary": "Executive summary for second article",
      "keyFindings": [
        {
          "priority": "MEDIUM",
          "title": "Secondary finding title",
          "description": "Secondary analysis",
          "businessImpact": "Secondary business impact"
        }
      ],
      "detailedAnalysis": "Second comprehensive analysis",
      "financialImpact": {
        "shortTerm": "Short term financial impact",
        "longTerm": "Long term financial impact",
        "investmentRequired": "Investment requirements"
      },
      "actionItems": [
        "Additional actionable steps"
      ],
      "citations": []
    }
  ],
  "overallRiskLevel": "HIGH|MEDIUM|LOW",
  "totalSources": 9
}

Focus on:
1. Regulatory compliance impacts (EU CBAM, plastic directives, UAE regulations)
2. Financial implications for €2.3B EU exports and $1.8B Asian markets
3. Competitive positioning vs SABIC, Dow, ExxonMobil, LyondellBasell
4. Sustainability opportunities and ESG compliance requirements
5. Specific actionable recommendations for Borouge leadership

Ensure exactly 2 articles with priority-based classification.`;

    return prompt;
  }

  // Simplified Gemini API integration (Single Provider)
  async analyzeWithGemini(query, isFollowUp = false, conversationContext = null) {
    if (!this.checkRateLimit()) {
      throw new Error('Gemini rate limit exceeded. Please try again in a moment.');
    }

    const prompt = this.createAnalysisPrompt(query, isFollowUp, conversationContext);

    try {
      console.log(`🔄 Making Gemini API request...`);
      const requestBody = {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.3,
          maxOutputTokens: 2500,
          topP: 0.8,
          topK: 40
        }
      };

      const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${this.config.gemini.apiKey}`;
      console.log(`📤 Gemini request URL: ${apiUrl.replace(this.config.gemini.apiKey, 'API_KEY_HIDDEN')}`);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      console.log(`📥 Gemini response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Gemini API error response: ${errorText}`);
        throw new Error(`Gemini API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      console.log(`📊 Gemini response structure:`, Object.keys(data));

      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content || !data.candidates[0].content.parts) {
        console.error(`❌ Unexpected Gemini response structure:`, data);
        throw new Error('Invalid Gemini API response structure');
      }

      const content = data.candidates[0].content.parts[0].text;

      // Return the raw content for the response parser to handle
      console.log(`✅ Gemini API response received (${content.length} chars)`);
      return content;

    } catch (error) {
      console.error('❌ Gemini API error:', error.message);
      throw error;
    }
  }



  // Simplified Gemini-only analysis with conversational follow-up support and real citations
  async analyzeQuery(query, isFollowUp = false, conversationId = null) {
    const startTime = Date.now();

    console.log(`🔍 Starting Gemini analysis with real citations (follow-up: ${isFollowUp})`);

    // Get conversation context for follow-ups
    let conversationContext = null;
    if (isFollowUp && conversationId) {
      conversationContext = this.getConversationContext(conversationId);
      if (conversationContext) {
        conversationContext.followUpCount++;
        console.log(`📝 Using conversation context (follow-up #${conversationContext.followUpCount})`);
      } else {
        console.warn(`⚠️ Conversation context not found for ID: ${conversationId}`);
        isFollowUp = false; // Treat as new query if context is missing
      }
    }

    try {
      // Step 1: Fetch real citations from Google Search
      console.log(`🔍 Fetching real citations for: "${query}"`);
      const citations = await this.searchService.getCitationsForQuery(query, 40);
      console.log(`📚 Retrieved ${citations.length} real citations`);

      // Step 2: Perform analysis with Gemini
      console.log(`🚀 Starting analysis with Gemini...`);
      const rawResponse = await this.analyzeWithGemini(query, isFollowUp, conversationContext);
      console.log(`📝 Raw response received from Gemini: ${typeof rawResponse} (${rawResponse ? rawResponse.length : 0} chars)`);

      // Step 3: Parse and validate response
      console.log(`🔍 Parsing response from Gemini...`);
      const parseResult = await this.responseParser.parseResponse(rawResponse, 'gemini', query);
      console.log(`📊 Parse result: success=${parseResult.success}, quality=${parseResult.qualityScore || 'N/A'}`);

      const responseTime = Date.now() - startTime;

      if (parseResult.success) {
        // Step 4: Integrate real citations into articles
        console.log(`🔗 Integrating ${citations.length} real citations into articles`);
        parseResult.data = this.integrateCitations(parseResult.data, citations);

        // Generate conversation ID for new queries
        let newConversationId = conversationId;
        if (!isFollowUp) {
          newConversationId = this.generateConversationId(query);
          this.storeConversationContext(newConversationId, query, parseResult.data);
        } else if (conversationContext) {
          // Update existing conversation context
          this.conversationHistory.set(conversationId, conversationContext);
        }

        // Add conversation metadata to response
        parseResult.data.conversationId = newConversationId;
        parseResult.data.isFollowUp = isFollowUp;

        // Track analytics with real citation count
        await this.trackAnalytics(query, 'gemini', responseTime, citations.length, parseResult.qualityScore);

        console.log(`✅ Analysis completed successfully with Gemini (${responseTime}ms, quality: ${parseResult.qualityScore}, citations: ${citations.length})`);
        return parseResult.data;
      } else {
        throw new Error('Gemini failed to generate valid response structure');
      }

    } catch (error) {
      console.error(`❌ Gemini analysis failed: ${error.message}`);

      // Track failure analytics
      await this.trackAnalytics(query, 'failed', Date.now() - startTime, 0, 0);

      throw new Error(`Gemini API analysis failed: ${error.message}. Please try again or contact support.`);
    }
  }

  // Integrate real citations into AI-generated articles
  integrateCitations(responseData, citations) {
    if (!responseData.articles || !Array.isArray(responseData.articles)) {
      return responseData;
    }

    // Distribute citations across articles (5-8 per article)
    const citationsPerArticle = Math.ceil(citations.length / responseData.articles.length);

    responseData.articles.forEach((article, index) => {
      const startIndex = index * citationsPerArticle;
      const endIndex = Math.min(startIndex + citationsPerArticle, citations.length);
      const articleCitations = citations.slice(startIndex, endIndex);

      // Ensure minimum 5 citations per article
      while (articleCitations.length < 5 && citations.length > 0) {
        const randomCitation = citations[Math.floor(Math.random() * citations.length)];
        if (!articleCitations.find(c => c.url === randomCitation.url)) {
          articleCitations.push(randomCitation);
        }
      }

      article.citations = articleCitations.slice(0, 8); // Maximum 8 per article
      article.sources = article.citations.length; // Update source count
    });

    // Update total sources count
    responseData.totalSources = citations.length;

    console.log(`🔗 Integrated citations: ${responseData.articles.map(a => a.citations.length).join(', ')} per article`);

    return responseData;
  }

  // Get simplified provider statistics (Gemini only)
  getProviderStatistics() {
    const now = Date.now();
    const windowStart = now - this.rateLimitWindow;
    const recentRequests = this.rateLimitTracking.filter(time => time > windowStart);

    return {
      gemini: {
        health: {
          status: 'healthy',
          availability: 100,
          responseTime: 0, // Would need to track this separately
          qualityScore: 95 // Default high score for Gemini
        },
        metrics: {
          totalRequests: this.rateLimitTracking.length,
          recentRequests: recentRequests.length,
          rateLimitUsage: `${recentRequests.length}/${this.rateLimitMax}`,
          successRate: 100 // Simplified - would need error tracking
        },
        rateLimit: {
          available: this.rateLimitMax - recentRequests.length,
          total: this.rateLimitMax,
          percentage: ((this.rateLimitMax - recentRequests.length) / this.rateLimitMax) * 100
        }
      }
    };
  }

  // Get conversation statistics
  getConversationStatistics() {
    const activeConversations = Array.from(this.conversationHistory.values())
      .filter(context => Date.now() - context.timestamp < this.conversationTimeout);

    return {
      activeConversations: activeConversations.length,
      totalConversations: this.conversationHistory.size,
      averageFollowUps: activeConversations.length > 0
        ? activeConversations.reduce((sum, conv) => sum + conv.followUpCount, 0) / activeConversations.length
        : 0
    };
  }

  // Get provider health summary (simplified)
  getProviderHealthSummary() {
    return {
      status: 'healthy',
      providersHealthy: 1,
      providersTotal: 1,
      provider: 'gemini'
    };
  }

  // Simplified analytics tracking for Gemini-only system
  async trackAnalytics(query, aiProvider, responseTime, sourcesFound, qualityScore = null) {
    try {
      const analyticsData = {
        query: query,
        query_type: this.categorizeQuery(query),
        response_time_ms: responseTime,
        sources_found: sourcesFound,
        user_rating: qualityScore ? Math.round(qualityScore / 20) : null, // Convert 0-100 to 1-5 scale
        follow_up_count: 0,
        created_at: new Date().toISOString()
      };

      await this.supabase
        .from('esg_query_analytics')
        .insert(analyticsData);

      console.log(`📊 Analytics tracked: ${aiProvider} | ${responseTime}ms | ${sourcesFound} sources | quality: ${qualityScore || 'N/A'}`);

    } catch (error) {
      console.error('Failed to track analytics:', error);
    }
  }

  // Categorize query for analytics
  categorizeQuery(query) {
    const lowerQuery = query.toLowerCase();

    if (lowerQuery.includes('regulation') || lowerQuery.includes('compliance') || lowerQuery.includes('cbam') || lowerQuery.includes('directive')) {
      return 'regulatory';
    } else if (lowerQuery.includes('financial') || lowerQuery.includes('cost') || lowerQuery.includes('investment') || lowerQuery.includes('revenue')) {
      return 'financial';
    } else if (lowerQuery.includes('competitor') || lowerQuery.includes('sabic') || lowerQuery.includes('dow') || lowerQuery.includes('exxon')) {
      return 'competitive';
    } else if (lowerQuery.includes('market') || lowerQuery.includes('trend') || lowerQuery.includes('demand')) {
      return 'market';
    } else if (lowerQuery.includes('environment') || lowerQuery.includes('carbon') || lowerQuery.includes('emission') || lowerQuery.includes('sustainability')) {
      return 'environmental';
    } else if (lowerQuery.includes('governance') || lowerQuery.includes('reporting') || lowerQuery.includes('stakeholder')) {
      return 'governance';
    } else {
      return 'strategic';
    }
  }
}

module.exports = AIService;
